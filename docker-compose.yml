version: '3.8'

services:
  api:
    build: .
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
    volumes:
      - .:/app
    ports:
      - "8000:8000"
    environment:
      - ENV_MODE=development
      - SQLITE_DATABASE_URI=sqlite:///./isms.db
    networks:
      - isms-network
    depends_on:
      - db
  
  db:
    image: postgres:15-alpine
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=isms
    ports:
      - "5432:5432"
    networks:
      - isms-network

networks:
  isms-network:

volumes:
  postgres_data:
